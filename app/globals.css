@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile placeholder text visibility fixes */
@media (max-width: 768px) {
  input::placeholder,
  textarea::placeholder {
    color: #6b7280 !important; /* gray-500 for better visibility */
    opacity: 1 !important;
  }

  /* Specific fixes for form inputs */
  input[type="email"]::placeholder,
  input[type="password"]::placeholder,
  input[type="text"]::placeholder {
    color: #4b5563 !important; /* gray-600 for even better contrast */
    opacity: 1 !important;
  }

  /* Dark background form fixes */
  .bg-white input::placeholder {
    color: #6b7280 !important;
  }

  /* Focus state improvements for mobile */
  input:focus::placeholder,
  textarea:focus::placeholder {
    color: #9ca3af !important; /* gray-400 when focused */
    opacity: 0.7 !important;
  }

  /* Improve text contrast on mobile for prose content - light mode only */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }

  /* Improve text contrast on mobile for prose content */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }

  /* Dark mode prose content for mobile */
  @media (prefers-color-scheme: dark) {
    .prose p,
    .prose li,
    .prose-gray p,
    .prose-gray li {
      color: #e5e7eb !important; /* gray-200 for dark mode readability */
    }

    .prose h1,
    .prose h2,
    .prose h3,
    .prose h4,
    .prose h5,
    .prose h6,
    .prose-gray h1,
    .prose-gray h2,
    .prose-gray h3,
    .prose-gray h4,
    .prose-gray h5,
    .prose-gray h6 {
      color: #f9fafb !important; /* gray-50 for dark mode headings */
    }
  }

.ebook-reader-content li {
  /* List items should inherit color */
  color: inherit !important;
}

/* Ensure zen mode text is visible on all devices */
.prose-invert p,
.prose-invert li,
.prose-invert span,
.prose-invert div {
  color: #ffffff !important;
}

.prose-invert h1,
.prose-invert h2,
.prose-invert h3,
.prose-invert h4,
.prose-invert h5,
.prose-invert h6 {
  color: #f9fafb !important;
}

  /* Ensure button colors are consistent on mobile */
  .bg-blue-600 {
    background-color: #2563eb !important;
  }

  .bg-purple-600 {
    background-color: #9333ea !important;
  }

  .bg-gray-100 {
    background-color: #f3f4f6 !important;
  }

  /* Improve button text contrast on mobile */
  button .text-gray-700 {
    color: #374151 !important;
  }
}
